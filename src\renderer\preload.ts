import { contextBridge, ipc<PERSON>enderer } from 'electron';
import { Configuration, MCPServerConfig, APIResponse } from '@shared/types';

// 定义 Electron API 接口
export interface ElectronAPI {
  // 应用信息
  getAppVersion: () => Promise<string>;
  
  // 消息框
  showMessageBox: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue | null>;
  
  // 视频播放相关
  onOpenVideoFile: (callback: (event: Electron.IpcRendererEvent, filePath: string) => void) => void;
  onTogglePlay: (callback: (event: Electron.IpcRendererEvent) => void) => void;
  onStopVideo: (callback: (event: Electron.IpcRendererEvent) => void) => void;
  
  // AI聊天相关
  onToggleChatPanel: (callback: (event: Electron.IpcRendererEvent) => void) => void;
  
  // 移除监听器
  removeAllListeners: (channel: string) => void;
  
  // MCP 客户端相关
  mcpConnect: (serverConfig: MCPServerConfig) => Promise<APIResponse>;
  mcpDisconnect: (serverId: string) => Promise<APIResponse>;
  mcpSendMessage: (serverId: string, message: any) => Promise<APIResponse>;
  onMcpMessage: (callback: (event: Electron.IpcRendererEvent, data: any) => void) => void;
  
  // 网络视频播放相关
  validateVideoUrl: (url: string) => Promise<boolean>;
  
  // 文件系统相关
  selectVideoFile: () => Promise<string | null>;
  
  // 设置相关
  getSettings: () => Promise<Configuration>;
  saveSettings: (settings: Partial<Configuration>) => Promise<boolean>;
  
  // 窗口控制
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;

  // AI 聊天功能
  aiChat: (messages: Array<{role: string; content: string}>, sessionId?: string) => Promise<APIResponse>;

  // 聊天会话管理
  createChatSession: (title: string) => Promise<APIResponse>;
  getChatSessions: () => Promise<APIResponse>;
  getChatMessages: (sessionId: string, limit?: number) => Promise<APIResponse>;
  deleteChatSession: (sessionId: string) => Promise<APIResponse>;

  // 文件搜索功能
  indexFiles: (paths: string[]) => Promise<APIResponse>;
  searchFiles: (query: string, options?: any) => Promise<APIResponse>;

  // OpenAI 配置
  getOpenaiConfig: () => Promise<any>;
  updateOpenaiConfig: (config: any) => Promise<APIResponse>;
  testOpenaiConnection: () => Promise<APIResponse>;

  // MCP 服务器管理
  getMcpServers: () => Promise<APIResponse>;
  addRemoteMcpServer: (config: any) => Promise<APIResponse>;
  removeMcpServer: (serverId: string) => Promise<APIResponse>;
  getAvailableTools: () => Promise<APIResponse>;
}

// 暴露受保护的方法，允许渲染进程使用
const electronAPI: ElectronAPI = {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 消息框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // 视频播放相关
  onOpenVideoFile: (callback) => ipcRenderer.on('open-video-file', callback),
  onTogglePlay: (callback) => ipcRenderer.on('toggle-play', callback),
  onStopVideo: (callback) => ipcRenderer.on('stop-video', callback),
  
  // AI聊天相关
  onToggleChatPanel: (callback) => ipcRenderer.on('toggle-chat-panel', callback),
  
  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // MCP 客户端相关
  mcpConnect: (serverConfig) => ipcRenderer.invoke('mcp-connect', serverConfig),
  mcpDisconnect: (serverId) => ipcRenderer.invoke('mcp-disconnect', serverId),
  mcpSendMessage: (serverId, message) => ipcRenderer.invoke('mcp-send-message', serverId, message),
  onMcpMessage: (callback) => ipcRenderer.on('mcp-message', callback),
  
  // 网络视频播放相关
  validateVideoUrl: (url) => ipcRenderer.invoke('validate-video-url', url),
  
  // 文件系统相关
  selectVideoFile: () => ipcRenderer.invoke('select-video-file'),
  
  // 设置相关
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // 窗口控制
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),

  // AI 聊天功能
  aiChat: (messages, sessionId) => ipcRenderer.invoke('ai-chat', messages, sessionId),

  // 聊天会话管理
  createChatSession: (title) => ipcRenderer.invoke('create-chat-session', title),
  getChatSessions: () => ipcRenderer.invoke('get-chat-sessions'),
  getChatMessages: (sessionId, limit) => ipcRenderer.invoke('get-chat-messages', sessionId, limit),
  deleteChatSession: (sessionId) => ipcRenderer.invoke('delete-chat-session', sessionId),

  // 文件搜索功能
  indexFiles: (paths) => ipcRenderer.invoke('index-files', paths),
  searchFiles: (query, options) => ipcRenderer.invoke('search-files', query, options),

  // OpenAI 配置
  getOpenaiConfig: () => ipcRenderer.invoke('get-openai-config'),
  updateOpenaiConfig: (config) => ipcRenderer.invoke('update-openai-config', config),
  testOpenaiConnection: () => ipcRenderer.invoke('test-openai-connection'),

  // MCP 服务器管理
  getMcpServers: () => ipcRenderer.invoke('get-mcp-servers'),
  addRemoteMcpServer: (config) => ipcRenderer.invoke('add-remote-mcp-server', config),
  removeMcpServer: (serverId) => ipcRenderer.invoke('remove-mcp-server', serverId),
  getAvailableTools: () => ipcRenderer.invoke('get-available-tools'),
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 在窗口加载完成后执行的初始化代码
window.addEventListener('DOMContentLoaded', () => {
  console.log('AI Player 预加载脚本已加载');
});

// 声明全局类型
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
