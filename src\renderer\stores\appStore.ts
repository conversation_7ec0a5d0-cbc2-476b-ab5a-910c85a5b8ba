import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  Configuration, 
  ChatMessage, 
  MCPConnection, 
  VideoInfo,
  MCPServerConfig 
} from '@shared/types';

interface AppState {
  // 应用状态
  isReady: boolean;
  appVersion: string;
  
  // UI 状态
  isChatPanelOpen: boolean;
  isSettingsModalOpen: boolean;
  settingsDefaultTab: number;
  
  // 视频状态
  currentVideo: VideoInfo | null;
  
  // 聊天状态
  chatMessages: ChatMessage[];
  currentSessionId: string | null;
  chatSessions: Array<{
    id: string;
    title: string;
    createdAt: Date;
    updatedAt: Date;
    messageCount: number;
  }>;

  // MCP 状态
  mcpConnections: MCPConnection[];
  availableTools: Array<{
    serverId: string;
    serverName: string;
    serverType: 'local' | 'remote';
    tools: string[];
  }>;
  
  // 设置
  settings: Configuration | null;
  
  // Actions
  initializeApp: () => Promise<void>;
  setupElectronListeners: () => void;
  
  // UI Actions
  toggleChatPanel: () => void;
  openSettingsModal: (defaultTab?: number) => void;
  closeSettingsModal: () => void;
  
  // 视频 Actions
  loadVideo: (src: string, title?: string) => void;
  updateVideoInfo: (info: Partial<VideoInfo>) => void;
  
  // 聊天 Actions
  addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  clearChatMessages: () => void;
  sendAIMessage: (content: string) => Promise<void>;
  createChatSession: (title: string) => Promise<string | null>;
  loadChatSessions: () => Promise<void>;
  switchChatSession: (sessionId: string) => Promise<void>;
  deleteChatSession: (sessionId: string) => Promise<void>;
  
  // MCP Actions
  connectMCP: (serverConfig: MCPServerConfig) => Promise<boolean>;
  disconnectMCP: (serverId: string) => Promise<boolean>;
  updateMCPConnection: (id: string, updates: Partial<MCPConnection>) => void;
  
  // 设置 Actions
  loadSettings: () => Promise<void>;
  saveSettings: (settings: Partial<Configuration>) => Promise<void>;
  refreshOpenAIConfig: () => Promise<void>;
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      isReady: false,
      appVersion: '',
      isChatPanelOpen: true, // 默认展开聊天面板
      isSettingsModalOpen: false,
      settingsDefaultTab: 0,
      currentVideo: null,
      chatMessages: [],
      currentSessionId: null,
      chatSessions: [],
      mcpConnections: [],
      availableTools: [],
      settings: null,

      // 初始化应用
      initializeApp: async () => {
        try {
          let version = 'Unknown';

          // 检查是否在 Electron 环境中
          if (typeof window !== 'undefined' && window.electronAPI) {
            // 获取应用版本
            version = await window.electronAPI.getAppVersion();

            // 加载设置
            await get().loadSettings();
          } else {
            console.warn('在浏览器环境中运行，某些功能可能不可用');
            // 在浏览器环境中设置默认设置
            set({ settings: null });
          }

          // 添加欢迎消息
          get().addChatMessage({
            role: 'system',
            content: '您好！我是AI助手，可以帮您解答问题或聊天。',
          });

          set({
            isReady: true,
            appVersion: version
          });

          console.log('AI Player 初始化完成');
        } catch (error) {
          console.error('应用初始化失败:', error);
          // 即使初始化失败，也要设置为就绪状态，以便用户可以看到界面
          set({
            isReady: true,
            appVersion: 'Error'
          });
        }
      },

      // 设置 Electron 事件监听器
      setupElectronListeners: () => {
        // 检查是否在 Electron 环境中
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，跳过事件监听器设置');
          return;
        }

        try {
          // 视频文件打开
          window.electronAPI.onOpenVideoFile((_, filePath) => {
            const fileName = filePath.split(/[\\/]/).pop() || '未知文件';
            get().loadVideo(`file://${filePath}`, fileName);
          });

          // 播放/暂停切换
          window.electronAPI.onTogglePlay(() => {
            const video = document.querySelector('video');
            if (video) {
              if (video.paused) {
                video.play();
              } else {
                video.pause();
              }
            }
          });

          // 停止视频
          window.electronAPI.onStopVideo(() => {
            const video = document.querySelector('video');
            if (video) {
              video.pause();
              video.currentTime = 0;
            }
          });

          // 切换聊天面板
          window.electronAPI.onToggleChatPanel(() => {
            get().toggleChatPanel();
          });

          // MCP 消息
          window.electronAPI.onMcpMessage((_, data) => {
            console.log('收到 MCP 消息:', data);
            // 处理 MCP 消息
          });
        } catch (error) {
          console.error('设置 Electron 事件监听器失败:', error);
        }
      },

      // UI Actions
      toggleChatPanel: () => {
        set(state => ({ 
          isChatPanelOpen: !state.isChatPanelOpen 
        }));
      },

      openSettingsModal: (defaultTab: number = 0) => {
        // 确保defaultTab是一个有效的数字
        const validTab = typeof defaultTab === 'number' && !isNaN(defaultTab) && defaultTab >= 0 && defaultTab <= 2
          ? defaultTab
          : 0;

        set({
          isSettingsModalOpen: true,
          settingsDefaultTab: validTab
        });
      },

      closeSettingsModal: () => {
        set({ isSettingsModalOpen: false });
      },

      // 视频 Actions
      loadVideo: (src: string, title = '视频') => {
        set({
          currentVideo: {
            title,
            src,
            duration: 0,
            currentTime: 0,
            volume: 50,
            muted: false,
            paused: true,
            ended: false,
          },
        });
      },

      updateVideoInfo: (info: Partial<VideoInfo>) => {
        set(state => ({
          currentVideo: state.currentVideo 
            ? { ...state.currentVideo, ...info }
            : null,
        }));
      },

      // 聊天 Actions
      addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
        // 生成唯一ID，结合时间戳和随机数
        const uniqueId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        const newMessage: ChatMessage = {
          ...message,
          id: uniqueId,
          timestamp: new Date(),
        };

        set(state => ({
          chatMessages: [...state.chatMessages, newMessage],
        }));
      },

      clearChatMessages: () => {
        set({ chatMessages: [] });
      },

      // AI 聊天功能
      sendAIMessage: async (content: string) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法发送AI消息');
          return;
        }

        try {
          const state = get();

          // 添加用户消息到界面
          const userMessage: ChatMessage = {
            id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            role: 'user',
            content,
            timestamp: new Date(),
          };

          set(prevState => ({
            chatMessages: [...prevState.chatMessages, userMessage]
          }));

          // 准备发送给AI的消息历史
          const messages = state.chatMessages.slice(-10).map(msg => ({
            role: msg.role,
            content: msg.content
          }));
          messages.push({ role: 'user', content });

          // 发送到AI
          const response = await window.electronAPI.aiChat(messages, state.currentSessionId || undefined);

          if (response.success) {
            // 添加AI回复到界面
            const aiMessage: ChatMessage = {
              id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              role: 'assistant',
              content: response.data.response,
              timestamp: new Date(),
            };

            set(prevState => ({
              chatMessages: [...prevState.chatMessages, aiMessage]
            }));
          } else {
            console.error('AI chat failed:', response.error);
            // 添加错误消息
            const errorMessage: ChatMessage = {
              id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              role: 'assistant',
              content: '抱歉，我现在无法回复您的消息。请稍后再试。',
              timestamp: new Date(),
            };

            set(prevState => ({
              chatMessages: [...prevState.chatMessages, errorMessage]
            }));
          }
        } catch (error) {
          console.error('发送AI消息失败:', error);
        }
      },

      // 聊天会话管理
      createChatSession: async (title: string): Promise<string | null> => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法创建聊天会话');
          return null;
        }

        try {
          const response = await window.electronAPI.createChatSession(title);
          if (response.success) {
            await get().loadChatSessions();
            return response.data;
          }
          return null;
        } catch (error) {
          console.error('创建聊天会话失败:', error);
          return null;
        }
      },

      loadChatSessions: async () => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法加载聊天会话');
          return;
        }

        try {
          const response = await window.electronAPI.getChatSessions();
          if (response.success) {
            set({ chatSessions: response.data });
          }
        } catch (error) {
          console.error('加载聊天会话失败:', error);
        }
      },

      switchChatSession: async (sessionId: string) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法切换聊天会话');
          return;
        }

        try {
          const response = await window.electronAPI.getChatMessages(sessionId, 100);
          if (response.success) {
            set({
              currentSessionId: sessionId,
              chatMessages: response.data.reverse() // 按时间顺序排列
            });
          }
        } catch (error) {
          console.error('切换聊天会话失败:', error);
        }
      },

      deleteChatSession: async (sessionId: string) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法删除聊天会话');
          return;
        }

        try {
          const response = await window.electronAPI.deleteChatSession(sessionId);
          if (response.success) {
            await get().loadChatSessions();

            // 如果删除的是当前会话，清空消息
            const state = get();
            if (state.currentSessionId === sessionId) {
              set({
                currentSessionId: null,
                chatMessages: []
              });
            }
          }
        } catch (error) {
          console.error('删除聊天会话失败:', error);
        }
      },

      // MCP Actions
      connectMCP: async (serverConfig: MCPServerConfig) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法连接 MCP');
          return false;
        }

        try {
          const response = await window.electronAPI.mcpConnect(serverConfig);
          if (response.success && response.data) {
            set(state => ({
              mcpConnections: [...state.mcpConnections, response.data],
            }));
            return true;
          }
          return false;
        } catch (error) {
          console.error('MCP 连接失败:', error);
          return false;
        }
      },

      disconnectMCP: async (serverId: string) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法断开 MCP 连接');
          return false;
        }

        try {
          const response = await window.electronAPI.mcpDisconnect(serverId);
          if (response.success) {
            set(state => ({
              mcpConnections: state.mcpConnections.filter(
                conn => conn.id !== serverId
              ),
            }));
            return true;
          }
          return false;
        } catch (error) {
          console.error('MCP 断开连接失败:', error);
          return false;
        }
      },

      updateMCPConnection: (id: string, updates: Partial<MCPConnection>) => {
        set(state => ({
          mcpConnections: state.mcpConnections.map(conn =>
            conn.id === id ? { ...conn, ...updates } : conn
          ),
        }));
      },

      // 设置 Actions
      loadSettings: async () => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法加载设置');
          return;
        }

        try {
          const settings = await window.electronAPI.getSettings();
          set({ settings });
          console.log('设置已从数据库加载:', settings);
        } catch (error) {
          console.error('加载设置失败:', error);
        }
      },

      saveSettings: async (newSettings: Partial<Configuration>) => {
        if (typeof window === 'undefined' || !window.electronAPI) {
          console.warn('Electron API 不可用，无法保存设置');
          return;
        }

        try {
          const success = await window.electronAPI.saveSettings(newSettings);
          if (success) {
            set(state => ({
              settings: state.settings
                ? { ...state.settings, ...newSettings }
                : null,
            }));
            // 保存成功后刷新OpenAI配置
            await get().refreshOpenAIConfig();
          }
        } catch (error) {
          console.error('保存设置失败:', error);
        }
      },

      // 刷新OpenAI配置
      refreshOpenAIConfig: async () => {
        try {
          if (typeof window !== 'undefined' && window.electronAPI) {
            const settings = get().settings;
            if (settings && settings.ai) {
              const aiConfig = {
                apiKey: settings.ai.apiKey || '',
                baseURL: settings.ai.apiUrl || 'https://api.openai.com/v1',
                model: settings.ai.model || 'gpt-3.5-turbo',
                temperature: settings.ai.temperature || 0.7,
                maxTokens: settings.ai.maxTokens || 2000,
                systemPrompt: settings.ai.systemPrompt || '你是一个有用的AI助手。'
              };

              await window.electronAPI.updateOpenaiConfig(aiConfig);
              console.log('OpenAI配置已刷新:', aiConfig);
            }
          }
        } catch (error) {
          console.error('刷新OpenAI配置失败:', error);
        }
      },
    }),
    {
      name: 'ai-player-store',
    }
  )
);
