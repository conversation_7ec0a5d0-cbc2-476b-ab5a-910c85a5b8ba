{"name": "aiplayer", "version": "1.0.0", "description": "AI Player - 跨平台AI播放器软件", "main": "dist/main/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite --host 0.0.0.0", "cors-proxy": "node cors-proxy-server.js", "dev:electron": "wait-on http://localhost:3000 && npm run build:main && electron . --dev", "build": "npm run build:main && npm run build:renderer && npm run build:preload", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "build:preload": "tsc -p tsconfig.preload.json", "preview": "vite preview", "dist": "npm run build && electron-builder --publish=never", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css}\"", "type-check": "tsc --noEmit"}, "keywords": ["electron", "ai", "player", "video", "chat", "mcp"], "author": "AI Player Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^38.0.0", "electron-builder": "^26.0.12", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "vite": "^5.0.10", "wait-on": "^7.2.0"}, "build": {"appId": "com.aiplayer.app", "productName": "AI Player", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "assets/**/*"], "extraResources": [{"from": "assets", "to": "assets"}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@modelcontextprotocol/sdk": "^1.17.5", "@modelcontextprotocol/server-filesystem": "^2025.8.21", "@mui/icons-material": "^5.15.2", "@mui/material": "^5.15.2", "axios": "^1.11.0", "better-sqlite3": "^12.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "uuid": "^12.0.0", "ws": "^8.18.3", "zustand": "^4.4.7"}}