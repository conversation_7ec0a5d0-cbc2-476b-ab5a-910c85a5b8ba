// 本地文件搜索 MCP 服务器
import * as fs from 'fs';
import * as path from 'path';
import { databaseManager, FileSearchIndex } from './database';

export interface FileSearchOptions {
  query: string;
  fileTypes?: string[];
  maxResults?: number;
  includeContent?: boolean;
  searchPaths?: string[];
}

export interface FileSearchResult {
  filePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  content?: string;
  relevanceScore: number;
  matchedLines?: Array<{
    lineNumber: number;
    content: string;
  }>;
}

export interface IndexingOptions {
  paths: string[];
  fileTypes?: string[];
  maxFileSize?: number; // in bytes
  excludePatterns?: string[];
}

export class FileSearchMCPServer {
  private supportedFileTypes = new Set([
    '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c',
    '.h', '.css', '.html', '.xml', '.json', '.yaml', '.yml', '.ini', '.cfg',
    '.log', '.sql', '.sh', '.bat', '.ps1', '.php', '.rb', '.go', '.rs', '.swift'
  ]);

  private defaultExcludePatterns = [
    'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'target',
    '.DS_Store', 'Thumbs.db', '*.tmp', '*.temp', '*.log'
  ];

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log('File Search MCP Server initialized');
  }

  // 索引文件
  async indexFiles(options: IndexingOptions): Promise<{ indexed: number; errors: string[] }> {
    const { paths, fileTypes, maxFileSize = 10 * 1024 * 1024, excludePatterns = [] } = options;
    const allExcludePatterns = [...this.defaultExcludePatterns, ...excludePatterns];
    const allowedTypes = fileTypes ? new Set(fileTypes) : this.supportedFileTypes;
    
    let indexed = 0;
    const errors: string[] = [];

    for (const searchPath of paths) {
      try {
        await this.indexDirectory(searchPath, allowedTypes, maxFileSize, allExcludePatterns);
        indexed++;
      } catch (error) {
        errors.push(`Failed to index ${searchPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { indexed, errors };
  }

  private async indexDirectory(
    dirPath: string,
    allowedTypes: Set<string>,
    maxFileSize: number,
    excludePatterns: string[]
  ): Promise<void> {
    if (!fs.existsSync(dirPath)) {
      throw new Error(`Directory does not exist: ${dirPath}`);
    }

    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      // 检查是否应该排除
      if (this.shouldExclude(fullPath, excludePatterns)) {
        continue;
      }

      if (entry.isDirectory()) {
        // 递归处理子目录
        await this.indexDirectory(fullPath, allowedTypes, maxFileSize, excludePatterns);
      } else if (entry.isFile()) {
        // 处理文件
        const ext = path.extname(entry.name).toLowerCase();
        if (allowedTypes.has(ext)) {
          try {
            await this.indexFile(fullPath, maxFileSize);
          } catch (error) {
            console.error(`Failed to index file ${fullPath}:`, error);
          }
        }
      }
    }
  }

  private shouldExclude(filePath: string, excludePatterns: string[]): boolean {
    const normalizedPath = filePath.replace(/\\/g, '/');
    
    return excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        // 简单的通配符匹配
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(normalizedPath);
      } else {
        return normalizedPath.includes(pattern);
      }
    });
  }

  private async indexFile(filePath: string, maxFileSize: number): Promise<void> {
    const stats = fs.statSync(filePath);
    
    if (stats.size > maxFileSize) {
      console.warn(`File too large, skipping: ${filePath} (${stats.size} bytes)`);
      return;
    }

    const fileName = path.basename(filePath);
    const ext = path.extname(fileName).toLowerCase();
    const mimeType = this.getMimeType(ext);
    
    let content = '';
    try {
      content = fs.readFileSync(filePath, 'utf-8');
    } catch (error) {
      console.error(`Failed to read file ${filePath}:`, error);
      return;
    }

    const fileIndex: Omit<FileSearchIndex, 'id' | 'indexedAt'> = {
      filePath,
      fileName,
      fileSize: stats.size,
      mimeType,
      content,
      metadata: JSON.stringify({
        extension: ext,
        lastModified: stats.mtime.toISOString(),
        lines: content.split('\n').length
      })
    };

    await databaseManager.addFileToIndex(fileIndex);
  }

  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.jsx': 'application/javascript',
      '.tsx': 'application/typescript',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.h': 'text/x-chdr',
      '.css': 'text/css',
      '.html': 'text/html',
      '.xml': 'application/xml',
      '.json': 'application/json',
      '.yaml': 'application/yaml',
      '.yml': 'application/yaml',
    };

    return mimeTypes[extension] || 'text/plain';
  }

  // 搜索文件
  async searchFiles(options: FileSearchOptions): Promise<FileSearchResult[]> {
    const { query, maxResults = 50, includeContent = false } = options;
    
    if (!query.trim()) {
      return [];
    }

    // 从数据库搜索
    const dbResults = await databaseManager.searchFiles(query, maxResults);
    
    // 转换为搜索结果格式
    const results: FileSearchResult[] = dbResults.map(item => {
      const result: FileSearchResult = {
        filePath: item.filePath,
        fileName: item.fileName,
        fileSize: item.fileSize,
        mimeType: item.mimeType || 'text/plain',
        relevanceScore: this.calculateRelevanceScore(query, item),
      };

      if (includeContent && item.content) {
        result.content = item.content;
        result.matchedLines = this.findMatchedLines(query, item.content);
      }

      return result;
    });

    // 按相关性排序
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    return results.slice(0, maxResults);
  }

  private calculateRelevanceScore(query: string, item: FileSearchIndex): number {
    let score = 0;
    const queryLower = query.toLowerCase();
    const fileNameLower = item.fileName.toLowerCase();
    const contentLower = (item.content || '').toLowerCase();

    // 文件名匹配权重更高
    if (fileNameLower.includes(queryLower)) {
      score += 10;
    }

    // 内容匹配
    const contentMatches = (contentLower.match(new RegExp(queryLower, 'g')) || []).length;
    score += contentMatches;

    // 文件类型权重
    const ext = path.extname(item.fileName).toLowerCase();
    if (['.md', '.txt', '.js', '.ts', '.py'].includes(ext)) {
      score += 2;
    }

    return score;
  }

  private findMatchedLines(query: string, content: string): Array<{ lineNumber: number; content: string }> {
    const lines = content.split('\n');
    const queryLower = query.toLowerCase();
    const matchedLines: Array<{ lineNumber: number; content: string }> = [];

    lines.forEach((line, index) => {
      if (line.toLowerCase().includes(queryLower)) {
        matchedLines.push({
          lineNumber: index + 1,
          content: line.trim()
        });
      }
    });

    return matchedLines.slice(0, 10); // 限制返回的匹配行数
  }

  // MCP 工具接口
  async handleToolCall(toolName: string, args: Record<string, any>): Promise<any> {
    switch (toolName) {
      case 'file_search':
        return await this.searchFiles({
          query: args.query || '',
          fileTypes: args.fileTypes,
          maxResults: args.maxResults || 20,
          includeContent: args.includeContent || false,
          searchPaths: args.searchPaths
        });

      case 'index_files':
        return await this.indexFiles({
          paths: args.paths || [],
          fileTypes: args.fileTypes,
          maxFileSize: args.maxFileSize,
          excludePatterns: args.excludePatterns
        });

      case 'get_file_content':
        return await this.getFileContent(args.filePath);

      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  private async getFileContent(filePath: string): Promise<{ content: string; metadata: any }> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File does not exist: ${filePath}`);
    }

    const stats = fs.statSync(filePath);
    const content = fs.readFileSync(filePath, 'utf-8');

    return {
      content,
      metadata: {
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        lines: content.split('\n').length
      }
    };
  }

  // 清理索引
  async clearIndex(): Promise<void> {
    // 这里需要实现清理数据库中文件索引的逻辑
    console.log('File search index cleared');
  }

  // 获取索引统计
  async getIndexStats(): Promise<{ totalFiles: number; totalSize: number; lastIndexed: Date | null }> {
    // 这里需要实现获取索引统计的逻辑
    return {
      totalFiles: 0,
      totalSize: 0,
      lastIndexed: null
    };
  }
}

// 单例实例
export const fileSearchMCP = new FileSearchMCPServer();
