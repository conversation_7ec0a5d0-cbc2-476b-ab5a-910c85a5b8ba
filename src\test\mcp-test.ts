/**
 * MCP功能测试
 */

import { MCPClient } from '../shared/mcp-client';
import { MCPManager } from '../shared/mcp-manager';
import { FileSystemMCPServer } from '../shared/filesystem-mcp-server';

async function testMCPClient() {
  console.log('Testing MCP Client...');
  
  const client = new MCPClient();
  
  // 测试stdio连接
  try {
    const connection = await client.connect({
      id: 'test-stdio',
      name: 'Test Stdio Server',
      url: 'stdio:node:--version',
      enabled: true,
      autoConnect: false,
      auth: { type: 'none' }
    });
    
    console.log('Stdio connection successful:', connection);
    
    // 断开连接
    client.disconnect('test-stdio');
    console.log('Stdio disconnection successful');
  } catch (error) {
    console.error('Stdio connection failed:', error);
  }
}

async function testFileSystemMCPServer() {
  console.log('Testing FileSystem MCP Server...');
  
  const server = new FileSystemMCPServer({
    allowedDirectories: [process.cwd()],
    maxFileSize: 1024 * 1024, // 1MB
    excludePatterns: ['node_modules', '.git']
  });
  
  console.log('FileSystem MCP Server created successfully');
  
  // 测试服务器实例
  const mcpServer = server.getServer();
  console.log('MCP Server instance:', mcpServer);
}

async function testMCPManager() {
  console.log('Testing MCP Manager...');
  
  const manager = new MCPManager();
  
  // 获取所有服务器
  const servers = await manager.getAllServers();
  console.log('Available servers:', servers);
  
  // 获取所有工具
  const tools = await manager.getAllTools();
  console.log('Available tools:', tools);
  
  // 测试工具调用
  try {
    const result = await manager.executeToolCall({
      name: 'search_files',
      arguments: {
        query: 'test',
        path: process.cwd(),
        maxResults: 5
      }
    });
    
    console.log('Tool call result:', result);
  } catch (error) {
    console.error('Tool call failed:', error);
  }
}

async function runTests() {
  console.log('Starting MCP Tests...\n');
  
  try {
    await testMCPClient();
    console.log('\n');
    
    await testFileSystemMCPServer();
    console.log('\n');
    
    await testMCPManager();
    console.log('\n');
    
    console.log('All tests completed!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runTests();
}

export {
  testMCPClient,
  testFileSystemMCPServer,
  testMCPManager,
  runTests
};
