/**
 * MCP (Model Context Protocol) 客户端
 * 使用官方 @modelcontextprotocol/sdk 实现
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { MCPConnection, MCPServerConfig, MCPTool, MCPResource } from './types';

interface MCPClientConnection {
  id: string;
  client: Client;
  transport: StdioClientTransport | SSEClientTransport;
  config: MCPServerConfig;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  capabilities?: any;
  tools: MCPTool[];
  resources: MCPResource[];
}

type EventHandler = (data: any) => void;

export class MCPClient {
  private connections = new Map<string, MCPClientConnection>();
  private messageHandlers = new Map<string, EventHandler[]>();

  /**
   * 连接到MCP服务器
   */
  async connect(config: MCPServerConfig): Promise<MCPConnection> {
    const { id, url, name, auth } = config;

    if (this.connections.has(id)) {
      throw new Error(`MCP服务器 ${id} 已经连接`);
    }

    try {
      let transport: StdioClientTransport | SSEClientTransport;
      let process: ChildProcess | undefined;

      // 根据URL类型选择传输方式
      if (url.startsWith('stdio:')) {
        // stdio传输：格式为 stdio:command:arg1:arg2...
        const parts = url.split(':');
        if (parts.length < 2) {
          throw new Error('Invalid stdio URL format. Expected: stdio:command:arg1:arg2...');
        }

        const command = parts[1];
        const args = parts.slice(2);

        transport = new StdioClientTransport({
          command: command,
          args: args
        });

      } else if (url.startsWith('http://') || url.startsWith('https://')) {
        // HTTP SSE传输
        transport = new SSEClientTransport(new URL(url));
      } else {
        throw new Error(`Unsupported transport type for URL: ${url}`);
      }

      // 创建MCP客户端
      const client = new Client(
        {
          name: 'AI Player',
          version: '1.0.0'
        },
        {
          capabilities: {
            roots: {
              listChanged: true
            },
            sampling: {}
          }
        }
      );

      // 创建连接对象
      const connection: MCPClientConnection = {
        id,
        client,
        transport,
        process,
        config,
        status: 'connecting',
        capabilities: undefined,
        tools: [],
        resources: []
      };

      this.connections.set(id, connection);

      // 连接到传输层
      await client.connect(transport);

      // 初始化连接
      await this.initializeConnection(id);

      connection.status = 'connected';
      console.log(`MCP服务器 ${name} (${id}) 连接成功`);

      // 返回兼容的连接对象
      return {
        id,
        name,
        url,
        status: connection.status,
        capabilities: connection.capabilities,
        tools: connection.tools,
        resources: connection.resources
      };

    } catch (error) {
      this.connections.delete(id);
      console.error(`MCP服务器连接失败 ${name} (${id}):`, error);
      throw error;
    }
  }

  /**
   * 断开MCP服务器连接
   */
  disconnect(id: string): void {
    const connection = this.connections.get(id);
    if (!connection) {
      return;
    }

    try {
      // 关闭客户端连接
      connection.client.close();

      // 如果是stdio连接，终止子进程
      if (connection.process) {
        connection.process.kill();
      }

      connection.status = 'disconnected';
      this.connections.delete(id);

      console.log(`MCP服务器 ${connection.config.name} (${id}) 已断开连接`);
      this.emit('connection-closed', { id });
    } catch (error) {
      console.error(`断开MCP服务器连接失败 ${id}:`, error);
    }
  }

  /**
   * 初始化连接，获取服务器能力
   */
  private async initializeConnection(id: string): Promise<void> {
    const connection = this.connections.get(id);
    if (!connection) {
      throw new Error(`Connection ${id} not found`);
    }

    try {
      // 获取服务器能力
      const serverCapabilities = connection.client.getServerCapabilities();
      connection.capabilities = serverCapabilities;

      // 获取可用工具
      if (serverCapabilities?.tools) {
        const toolsResult = await connection.client.listTools();
        connection.tools = toolsResult.tools.map(tool => ({
          name: tool.name,
          description: tool.description || '',
          inputSchema: tool.inputSchema
        }));
      }

      // 获取可用资源
      if (serverCapabilities?.resources) {
        const resourcesResult = await connection.client.listResources();
        connection.resources = resourcesResult.resources.map(resource => ({
          uri: resource.uri,
          name: resource.name || '',
          description: resource.description || '',
          mimeType: resource.mimeType
        }));
      }

      console.log(`MCP服务器 ${connection.config.name} 初始化完成:`, {
        tools: connection.tools.length,
        resources: connection.resources.length
      });

    } catch (error) {
      console.error(`初始化MCP连接失败 ${id}:`, error);
      throw error;
    }
  }

  /**
   * 调用MCP工具
   */
  async callTool(id: string, toolName: string, args: any = {}): Promise<any> {
    const connection = this.connections.get(id);
    if (!connection || connection.status !== 'connected') {
      throw new Error(`MCP服务器 ${id} 未连接`);
    }

    try {
      const result = await connection.client.callTool({
        name: toolName,
        arguments: args
      });

      return result.content;
    } catch (error) {
      console.error(`调用MCP工具失败 ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * 获取MCP资源
   */
  async getResource(id: string, uri: string): Promise<any> {
    const connection = this.connections.get(id);
    if (!connection || connection.status !== 'connected') {
      throw new Error(`MCP服务器 ${id} 未连接`);
    }

    try {
      const result = await connection.client.readResource({ uri });
      return result.contents;
    } catch (error) {
      console.error(`获取MCP资源失败 ${uri}:`, error);
      throw error;
    }
  }

  /**
   * 获取连接状态
   */
  getConnection(id: string): MCPConnection | null {
    const connection = this.connections.get(id);
    if (!connection) {
      return null;
    }

    return {
      id: connection.id,
      name: connection.config.name,
      url: connection.config.url,
      status: connection.status,
      capabilities: connection.capabilities,
      tools: connection.tools,
      resources: connection.resources
    };
  }

  /**
   * 获取所有连接
   */
  getAllConnections(): MCPConnection[] {
    return Array.from(this.connections.values()).map(connection => ({
      id: connection.id,
      name: connection.config.name,
      url: connection.config.url,
      status: connection.status,
      capabilities: connection.capabilities,
      tools: connection.tools,
      resources: connection.resources
    }));
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(id: string, error: Error): void {
    const connection = this.connections.get(id);
    if (connection) {
      connection.status = 'error';
      this.emit('connection-error', { id, error: error.message });
    }
  }

  /**
   * 处理连接关闭
   */
  private handleConnectionClosed(id: string, code: number): void {
    const connection = this.connections.get(id);
    if (connection) {
      connection.status = 'disconnected';
      this.emit('connection-closed', { id, code });
    }
  }

  /**
   * 事件发射器
   */
  private emit(event: string, data: any): void {
    const handlers = this.messageHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Event handler error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: string, handler: EventHandler): void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, []);
    }
    this.messageHandlers.get(event)!.push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, handler: EventHandler): void {
    const handlers = this.messageHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
}