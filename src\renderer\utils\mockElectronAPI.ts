// Mock Electron API for browser development
import { browserOpenAIClient } from './browserOpenAIClient';
import { browserDatabaseManager } from './browserDatabase';
import {
  createConfigManager,
  createChatManager
} from '@shared/databaseInterface';

// 初始化数据库
let dbManager: any = null;
let configManager: any = null;
let chatManager: any = null;
let isInitialized = false;

const initDB = async () => {
  if (!isInitialized) {
    try {
      // 直接使用browserDatabaseManager
      dbManager = browserDatabaseManager;
      await dbManager.initialize();

      configManager = createConfigManager(dbManager);
      chatManager = createChatManager(dbManager);
      isInitialized = true;
      console.log('[Mock] Database initialized successfully');
    } catch (error) {
      console.error('[Mock] Database initialization failed:', error);
      throw error;
    }
  }
  return { dbManager, configManager, chatManager };
};

// 设置Mock Electron API的函数
export const setupMockElectronAPI = async () => {
  try {
    if (typeof window !== 'undefined' && !window.electronAPI) {
      console.log('Setting up mock Electron API for browser development');

      // 初始化数据库
      await initDB();

      // 设置mock API
      (window as any).electronAPI = mockElectronAPI;

      console.log('[Mock] Mock Electron API setup completed');
    }
  } catch (error) {
    console.error('[Mock] Failed to setup Mock Electron API:', error);
  }
};

export const mockElectronAPI = {
  // 应用信息
  getAppVersion: async (): Promise<string> => {
    console.log('[Mock] getAppVersion called');
    return Promise.resolve('1.0.0-dev');
  },
  
  // 消息框
  showMessageBox: async (options: any): Promise<any> => {
    console.log('[Mock] showMessageBox called with:', options);
    alert(`${options.title}: ${options.message}`);
    return Promise.resolve({ response: 0 });
  },
  
  // 视频播放相关
  onOpenVideoFile: (callback: any): void => {
    console.log('[Mock] onOpenVideoFile listener registered');
    // 在浏览器环境中不做任何事情
  },
  
  onTogglePlay: (callback: any): void => {
    console.log('[Mock] onTogglePlay listener registered');
    // 在浏览器环境中不做任何事情
  },
  
  onStopVideo: (callback: any): void => {
    console.log('[Mock] onStopVideo listener registered');
    // 在浏览器环境中不做任何事情
  },
  
  // AI聊天相关
  onToggleChatPanel: (callback: any): void => {
    console.log('[Mock] onToggleChatPanel listener registered');
    // 在浏览器环境中不做任何事情
  },
  
  // 移除监听器
  removeAllListeners: (channel: string): void => {
    console.log(`[Mock] removeAllListeners called for channel: ${channel}`);
    // 在浏览器环境中不做任何事情
  },
  
  // MCP 客户端相关
  mcpConnect: async (serverConfig: any): Promise<any> => {
    console.log('[Mock] mcpConnect called with:', serverConfig);
    return Promise.resolve({ success: false, error: 'Mock API - MCP not available in browser' });
  },
  
  mcpDisconnect: async (serverId: string): Promise<void> => {
    console.log(`[Mock] mcpDisconnect called for server: ${serverId}`);
    return Promise.resolve();
  },
  
  mcpSendMessage: async (serverId: string, message: any): Promise<any> => {
    console.log(`[Mock] mcpSendMessage called for server ${serverId} with:`, message);
    return Promise.resolve({ success: false, error: 'Mock API - MCP not available in browser' });
  },
  
  onMcpMessage: (callback: any): void => {
    console.log('[Mock] onMcpMessage listener registered');
    // 在浏览器环境中不做任何事情
  },
  
  // 网络视频播放相关
  validateVideoUrl: async (url: string): Promise<boolean> => {
    console.log(`[Mock] validateVideoUrl called with: ${url}`);
    try {
      new URL(url);
      return Promise.resolve(true);
    } catch {
      return Promise.resolve(false);
    }
  },
  
  // 文件系统相关
  selectVideoFile: async (): Promise<string | null> => {
    console.log('[Mock] selectVideoFile called');
    // 在浏览器环境中，我们可以创建一个文件输入元素
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'video/*,audio/*,.mp4,.webm,.ogg,.avi,.mov,.wmv,.flv,.mkv,.webp';
      input.multiple = false;

      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          console.log(`[Mock] Selected file: ${file.name}, type: ${file.type}, size: ${file.size}`);
          const url = URL.createObjectURL(file);
          console.log(`[Mock] Created blob URL: ${url}`);
          resolve(url);
        } else {
          resolve(null);
        }
      };

      input.oncancel = () => {
        console.log('[Mock] File selection cancelled');
        resolve(null);
      };

      // 确保输入元素被添加到DOM中以便在某些浏览器中正常工作
      input.style.display = 'none';
      document.body.appendChild(input);
      input.click();

      // 清理DOM
      setTimeout(() => {
        document.body.removeChild(input);
      }, 1000);
    });
  },
  
  // 设置相关
  getSettings: async (): Promise<any> => {
    console.log('[Mock] getSettings called');
    try {
      const { configManager } = await initDB();
      const configs = await configManager.getAllConfigs();

      // 构建兼容的设置对象
      const settings = {
        ai: configs.ai || {},
        network: configs.network || {},
        player: configs.player || {},
        ...configs.app
      };

      return Promise.resolve(settings);
    } catch (error) {
      console.error('[Mock] getSettings failed:', error);
      return Promise.resolve(null);
    }
  },

  saveSettings: async (settings: any): Promise<boolean> => {
    console.log('[Mock] saveSettings called with:', settings);
    try {
      const { configManager } = await initDB();

      // 分别保存不同类别的配置
      if (settings.ai) {
        await configManager.saveAIConfig(settings.ai);
      }
      if (settings.network) {
        await configManager.saveNetworkConfig(settings.network);
      }
      if (settings.player) {
        await configManager.savePlayerConfig(settings.player);
      }

      // 保存其他应用配置
      const appConfig = { ...settings };
      delete appConfig.ai;
      delete appConfig.network;
      delete appConfig.player;

      if (Object.keys(appConfig).length > 0) {
        await configManager.saveAppConfig(appConfig);
      }

      return Promise.resolve(true);
    } catch (error) {
      console.error('[Mock] saveSettings failed:', error);
      return Promise.resolve(false);
    }
  },
  
  // 窗口控制
  minimizeWindow: async (): Promise<void> => {
    console.log('[Mock] minimizeWindow called - not available in browser');
    return Promise.resolve();
  },
  
  maximizeWindow: async (): Promise<void> => {
    console.log('[Mock] maximizeWindow called - not available in browser');
    return Promise.resolve();
  },
  
  closeWindow: async (): Promise<void> => {
    console.log('[Mock] closeWindow called - closing browser tab');
    window.close();
    return Promise.resolve();
  },

  // AI 聊天相关
  aiChat: async (messages: Array<{role: string; content: string}>, sessionId?: string): Promise<{success: boolean; data?: any; error?: string}> => {
    console.log('[Mock] aiChat called with:', messages, sessionId);

    try {
      const { dbManager, chatManager } = await initDB();

      // 检查是否需要使用代理
      const config = browserOpenAIClient.getConfig();
      const needsProxy = !config.baseURL.includes('localhost') &&
                        !config.baseURL.includes('127.0.0.1') &&
                        !config.baseURL.includes('api.openai.com');

      if (needsProxy || config.proxy?.enabled) {
        console.log('[Mock] 使用代理模式进行API请求');
      } else {
        console.log('[Mock] 直接进行API请求');
      }

      // 使用真实的OpenAI客户端
      const openAIMessages = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }));
      const result = await browserOpenAIClient.sendMessageWithTools(openAIMessages, needsProxy);

      // 保存消息到数据库
      if (sessionId) {
        const userMessage = messages[messages.length - 1];
        if (userMessage) {
          // 添加用户消息
          await dbManager.addChatMessage({
            id: `msg_${Date.now()}_user_${Math.random().toString(36).substring(2, 11)}`,
            role: userMessage.role,
            content: userMessage.content,
            sessionId
          });

          // 添加AI回复
          await dbManager.addChatMessage({
            id: `msg_${Date.now()}_ai_${Math.random().toString(36).substring(2, 11)}`,
            role: 'assistant',
            content: result.response,
            sessionId
          });
        }
      }

      return { success: true, data: result };
    } catch (error) {
      console.error('[Mock] AI chat failed:', error);

      let errorMessage = 'AI chat failed';
      if (error instanceof Error) {
        errorMessage = error.message;

        // 为CORS问题提供特殊处理
        if (errorMessage.includes('CORS') || errorMessage.includes('Content Security Policy')) {
          errorMessage = `浏览器环境限制：${errorMessage}\n\n解决方案：\n1. 在Electron环境中使用（推荐）\n2. 使用支持CORS的API端点\n3. 设置本地代理服务器`;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // 聊天会话管理
  createChatSession: async (title: string): Promise<{success: boolean; data?: string; error?: string}> => {
    console.log('[Mock] createChatSession called with:', title);
    try {
      const { dbManager } = await initDB();
      const sessionId = await dbManager.createChatSession(title);
      return Promise.resolve({ success: true, data: sessionId });
    } catch (error) {
      console.error('[Mock] createChatSession failed:', error);
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create session'
      });
    }
  },

  getChatSessions: async (): Promise<{success: boolean; data?: any[]; error?: string}> => {
    console.log('[Mock] getChatSessions called');
    try {
      const { dbManager } = await initDB();
      const sessions = await dbManager.getChatSessions();
      return Promise.resolve({ success: true, data: sessions });
    } catch (error) {
      console.error('[Mock] getChatSessions failed:', error);
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get sessions'
      });
    }
  },

  getChatMessages: async (sessionId: string, limit?: number): Promise<{success: boolean; data?: any[]; error?: string}> => {
    console.log('[Mock] getChatMessages called with:', sessionId, limit);
    try {
      const { dbManager } = await initDB();
      const messages = await dbManager.getChatMessages(sessionId, limit);
      return Promise.resolve({ success: true, data: messages });
    } catch (error) {
      console.error('[Mock] getChatMessages failed:', error);
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get messages'
      });
    }
  },

  deleteChatSession: async (sessionId: string): Promise<{success: boolean; error?: string}> => {
    console.log('[Mock] deleteChatSession called with:', sessionId);
    try {
      const { dbManager } = await initDB();
      await dbManager.deleteChatSession(sessionId);
      return Promise.resolve({ success: true });
    } catch (error) {
      console.error('[Mock] deleteChatSession failed:', error);
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete session'
      });
    }
  },

  // 文件搜索相关
  indexFiles: async (paths: string[]): Promise<{success: boolean; data?: any; error?: string}> => {
    console.log('[Mock] indexFiles called with:', paths);
    return Promise.resolve({
      success: true,
      data: { success: true, message: 'Mock indexing completed' }
    });
  },

  searchFiles: async (query: string, options?: any): Promise<{success: boolean; data?: any; error?: string}> => {
    console.log('[Mock] searchFiles called with:', query, options);
    const mockResults = [
      {
        filePath: '/mock/path/example.txt',
        fileName: 'example.txt',
        fileSize: 1024,
        mimeType: 'text/plain',
        relevanceScore: 0.8
      }
    ];
    return Promise.resolve({ success: true, data: mockResults });
  },

  // OpenAI 配置
  getOpenaiConfig: async (): Promise<any> => {
    console.log('[Mock] getOpenaiConfig called');
    return Promise.resolve(browserOpenAIClient.getConfig());
  },

  updateOpenaiConfig: async (config: any): Promise<{success: boolean; error?: string}> => {
    console.log('[Mock] updateOpenaiConfig called with:', config);
    try {
      // 获取当前的代理设置
      const proxyConfig = {
        enabled: false,
        host: '',
        port: 8080
      };

      try {
        const appSettings = localStorage.getItem('ai-player-store');
        if (appSettings) {
          const settings = JSON.parse(appSettings);
          const state = settings.state;
          if (state && state.settings && state.settings.network && state.settings.network.proxy) {
            proxyConfig.enabled = state.settings.network.proxy.enabled || false;
            proxyConfig.host = state.settings.network.proxy.host || '';
            proxyConfig.port = state.settings.network.proxy.port || 8080;
          }
        }
      } catch (error) {
        console.warn('Failed to load proxy config:', error);
      }

      // 更新配置，包含代理设置
      const configWithProxy = {
        ...config,
        proxy: proxyConfig
      };

      browserOpenAIClient.updateConfig(configWithProxy);
      console.log('OpenAI配置已更新，包含代理设置:', configWithProxy);
      return Promise.resolve({ success: true });
    } catch (error) {
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update config'
      });
    }
  },

  testOpenaiConnection: async (): Promise<{success: boolean; error?: string}> => {
    console.log('[Mock] testOpenaiConnection called');
    try {
      const result = await browserOpenAIClient.testConnection();
      return Promise.resolve(result);
    } catch (error) {
      return Promise.resolve({
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      });
    }
  },

  // MCP 服务器管理
  getMcpServers: async (): Promise<{success: boolean; data?: any[]; error?: string}> => {
    console.log('[Mock] getMcpServers called');
    const mockServers = [
      {
        id: 'local_file_search',
        name: '本地文件搜索',
        type: 'local',
        status: 'connected',
        tools: ['file_search', 'index_files', 'get_file_content']
      }
    ];
    return Promise.resolve({ success: true, data: mockServers });
  },

  addRemoteMcpServer: async (config: any): Promise<{success: boolean; error?: string}> => {
    console.log('[Mock] addRemoteMcpServer called with:', config);
    return Promise.resolve({ success: true });
  },

  removeMcpServer: async (serverId: string): Promise<{success: boolean; error?: string}> => {
    console.log('[Mock] removeMcpServer called with:', serverId);
    return Promise.resolve({ success: true });
  },

  getAvailableTools: async (): Promise<{success: boolean; data?: any[]; error?: string}> => {
    console.log('[Mock] getAvailableTools called');
    const mockTools = [
      {
        serverId: 'local_file_search',
        serverName: '本地文件搜索',
        serverType: 'local',
        tools: ['file_search', 'index_files', 'get_file_content']
      }
    ];
    return Promise.resolve({ success: true, data: mockTools });
  },
};

// 检查是否在浏览器环境中运行，如果是则设置 mock API
// 注意：setupMockElectronAPI函数已在文件开头定义
