// OpenAI API 客户端
import { databaseManager } from './database';

export interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: OpenAIMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
}

export interface MCPToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface MCPToolResult {
  success: boolean;
  result?: any;
  error?: string;
}

export class OpenAIClient {
  private config: OpenAIConfig;
  private defaultConfig: OpenAIConfig = {
    apiKey: '',
    baseURL: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000,
    systemPrompt: '你是一个有用的AI助手。'
  };

  constructor() {
    this.config = { ...this.defaultConfig };
    this.loadConfig();
  }

  private async loadConfig(): Promise<void> {
    try {
      const apiKey = await databaseManager.getSetting('openai_api_key');
      const baseURL = await databaseManager.getSetting('openai_base_url');
      const model = await databaseManager.getSetting('openai_model');
      const temperature = await databaseManager.getSetting('openai_temperature');
      const maxTokens = await databaseManager.getSetting('openai_max_tokens');
      const systemPrompt = await databaseManager.getSetting('openai_system_prompt');

      if (apiKey) this.config.apiKey = apiKey;
      if (baseURL) this.config.baseURL = baseURL;
      if (model) this.config.model = model;
      if (temperature) this.config.temperature = parseFloat(temperature);
      if (maxTokens) this.config.maxTokens = parseInt(maxTokens);
      if (systemPrompt) this.config.systemPrompt = systemPrompt;
    } catch (error) {
      console.error('Failed to load OpenAI config:', error);
    }
  }

  async updateConfig(newConfig: Partial<OpenAIConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    
    // 保存到数据库
    if (newConfig.apiKey !== undefined) {
      await databaseManager.setSetting('openai_api_key', newConfig.apiKey, 'openai');
    }
    if (newConfig.baseURL !== undefined) {
      await databaseManager.setSetting('openai_base_url', newConfig.baseURL, 'openai');
    }
    if (newConfig.model !== undefined) {
      await databaseManager.setSetting('openai_model', newConfig.model, 'openai');
    }
    if (newConfig.temperature !== undefined) {
      await databaseManager.setSetting('openai_temperature', newConfig.temperature.toString(), 'openai');
    }
    if (newConfig.maxTokens !== undefined) {
      await databaseManager.setSetting('openai_max_tokens', newConfig.maxTokens.toString(), 'openai');
    }
    if (newConfig.systemPrompt !== undefined) {
      await databaseManager.setSetting('openai_system_prompt', newConfig.systemPrompt, 'openai');
    }
  }

  getConfig(): OpenAIConfig {
    return { ...this.config };
  }

  async sendMessage(messages: OpenAIMessage[]): Promise<OpenAIResponse> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // 添加系统提示
    const messagesWithSystem: OpenAIMessage[] = [];
    if (this.config.systemPrompt) {
      messagesWithSystem.push({
        role: 'system',
        content: this.config.systemPrompt
      });
    }
    messagesWithSystem.push(...messages);

    const requestBody = {
      model: this.config.model,
      messages: messagesWithSystem,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
    };

    try {
      const response = await fetch(`${this.config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: OpenAIResponse = await response.json();
      return data;
    } catch (error) {
      console.error('OpenAI API request failed:', error);
      throw error;
    }
  }

  // 解析响应中的MCP工具调用
  parseMCPToolCalls(content: string): MCPToolCall[] {
    const toolCalls: MCPToolCall[] = [];
    
    // 查找工具调用模式，例如：[TOOL:file_search]{"query": "example"}[/TOOL]
    const toolCallRegex = /\[TOOL:(\w+)\](.*?)\[\/TOOL\]/gs;
    let match;
    
    while ((match = toolCallRegex.exec(content)) !== null) {
      const toolName = match[1];
      const argsString = match[2].trim();
      
      try {
        const args = JSON.parse(argsString);
        toolCalls.push({
          name: toolName,
          arguments: args
        });
      } catch (error) {
        console.error(`Failed to parse tool arguments for ${toolName}:`, error);
      }
    }
    
    return toolCalls;
  }

  // 格式化工具调用结果
  formatToolResult(toolCall: MCPToolCall, result: MCPToolResult): string {
    if (result.success) {
      return `[TOOL_RESULT:${toolCall.name}]${JSON.stringify(result.result)}[/TOOL_RESULT]`;
    } else {
      return `[TOOL_ERROR:${toolCall.name}]${result.error}[/TOOL_ERROR]`;
    }
  }

  // 处理带有工具调用的对话
  async sendMessageWithTools(
    messages: OpenAIMessage[],
    toolExecutor: (toolCall: MCPToolCall) => Promise<MCPToolResult>
  ): Promise<{ response: OpenAIResponse; toolResults: MCPToolResult[] }> {
    const response = await this.sendMessage(messages);
    const assistantMessage = response.choices[0]?.message?.content || '';
    
    // 解析工具调用
    const toolCalls = this.parseMCPToolCalls(assistantMessage);
    const toolResults: MCPToolResult[] = [];
    
    if (toolCalls.length > 0) {
      // 执行工具调用
      for (const toolCall of toolCalls) {
        try {
          const result = await toolExecutor(toolCall);
          toolResults.push(result);
        } catch (error) {
          toolResults.push({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
      
      // 如果有工具调用，需要再次发送消息包含工具结果
      if (toolResults.length > 0) {
        const toolResultsText = toolCalls.map((call, index) => 
          this.formatToolResult(call, toolResults[index])
        ).join('\n');
        
        const updatedMessages: OpenAIMessage[] = [
          ...messages,
          { role: 'assistant', content: assistantMessage },
          { role: 'user', content: `工具执行结果：\n${toolResultsText}\n\n请基于这些结果给出最终回答。` }
        ];
        
        const finalResponse = await this.sendMessage(updatedMessages);
        return { response: finalResponse, toolResults };
      }
    }
    
    return { response, toolResults };
  }

  // 测试API连接
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const testMessages: OpenAIMessage[] = [
        { role: 'user', content: 'Hello, this is a test message.' }
      ];
      
      await this.sendMessage(testMessages);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// 单例实例
export const openaiClient = new OpenAIClient();
