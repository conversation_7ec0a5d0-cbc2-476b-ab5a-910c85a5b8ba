// 浏览器环境下的OpenAI客户端
export interface OpenAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface OpenAIConfig {
  apiKey: string;
  baseURL: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  // 代理配置
  proxy?: {
    enabled: boolean;
    host: string;
    port: number;
  };
}

export interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: OpenAIMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class BrowserOpenAIClient {
  private config: OpenAIConfig;

  constructor() {
    this.config = this.loadConfig();
    // 监听应用设置变化
    this.setupSettingsListener();
  }

  private setupSettingsListener() {
    // 监听localStorage变化，当应用设置更新时自动刷新配置
    window.addEventListener('storage', (e) => {
      if (e.key === 'ai-player-store') {
        console.log('检测到应用设置变化，刷新OpenAI配置');
        this.config = this.loadConfig();
      }
    });
  }

  // 手动刷新配置的方法
  refreshConfig() {
    this.config = this.loadConfig();
    console.log('OpenAI配置已刷新:', this.config);
  }

  private loadConfig(): OpenAIConfig {
    try {
      // 首先尝试从openai-config加载
      const savedConfig = localStorage.getItem('openai-config');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        // 确保包含代理配置
        if (!config.proxy) {
          config.proxy = this.loadProxyConfig();
        }
        return config;
      }

      // 如果没有openai-config，尝试从应用设置中加载
      const appSettings = localStorage.getItem('ai-player-store');
      if (appSettings) {
        const settings = JSON.parse(appSettings);
        const state = settings.state;
        if (state && state.settings) {
          return {
            apiKey: state.settings.ai?.apiKey || '',
            baseURL: state.settings.ai?.apiUrl || 'https://api.openai.com/v1',
            model: state.settings.ai?.model || 'gpt-3.5-turbo',
            temperature: state.settings.ai?.temperature || 0.7,
            maxTokens: state.settings.ai?.maxTokens || 2000,
            systemPrompt: state.settings.ai?.systemPrompt || '你是一个有用的AI助手。',
            proxy: {
              enabled: state.settings.network?.proxy?.enabled || false,
              host: state.settings.network?.proxy?.host || '',
              port: state.settings.network?.proxy?.port || 8080
            }
          };
        }
      }
    } catch (error) {
      console.warn('Failed to load OpenAI config from localStorage:', error);
    }

    // 默认配置
    return {
      apiKey: '',
      baseURL: 'https://api.openai.com/v1',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000,
      systemPrompt: '你是一个有用的AI助手。',
      proxy: {
        enabled: false,
        host: '',
        port: 8080
      }
    };
  }

  private loadProxyConfig() {
    try {
      const appSettings = localStorage.getItem('ai-player-store');
      if (appSettings) {
        const settings = JSON.parse(appSettings);
        const state = settings.state;
        if (state && state.settings && state.settings.network) {
          return {
            enabled: state.settings.network.proxy?.enabled || false,
            host: state.settings.network.proxy?.host || '',
            port: state.settings.network.proxy?.port || 8080
          };
        }
      }
    } catch (error) {
      console.warn('Failed to load proxy config:', error);
    }

    return {
      enabled: false,
      host: '',
      port: 8080
    };
  }

  updateConfig(newConfig: Partial<OpenAIConfig>): void {
    // 更新配置，同时刷新代理设置
    const proxyConfig = this.loadProxyConfig();
    this.config = {
      ...this.config,
      ...newConfig,
      proxy: newConfig.proxy || proxyConfig
    };

    try {
      localStorage.setItem('openai-config', JSON.stringify(this.config));
      console.log('OpenAI配置已更新:', this.config);
    } catch (error) {
      console.warn('Failed to save OpenAI config to localStorage:', error);
    }
  }

  getConfig(): OpenAIConfig {
    return { ...this.config };
  }

  async sendMessage(messages: OpenAIMessage[], needsProxy: boolean = false): Promise<OpenAIResponse> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // 添加系统提示
    const messagesWithSystem: OpenAIMessage[] = [];
    if (this.config.systemPrompt) {
      messagesWithSystem.push({
        role: 'system',
        content: this.config.systemPrompt
      });
    }
    messagesWithSystem.push(...messages);

    const requestBody = {
      model: this.config.model,
      messages: messagesWithSystem,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
    };

    try {
      // 决定是否使用代理：优先使用配置中的代理设置，其次使用传入的needsProxy参数
      const shouldUseProxy = this.config.proxy?.enabled || needsProxy;

      let ai_url: string;
      if (shouldUseProxy) {
        // 如果配置了自定义代理，使用自定义代理
        if (this.config.proxy?.enabled && this.config.proxy.host) {
          const proxyHost = this.config.proxy.host;
          const proxyPort = this.config.proxy.port || 8080;
          ai_url = `http://${proxyHost}:${proxyPort}/proxy?url=${encodeURIComponent(this.config.baseURL + '/chat/completions')}`;
          console.log('使用自定义代理:', ai_url);
        } else {
          // 使用默认的CORS代理
          ai_url = 'http://localhost:8080/proxy?url=' + encodeURIComponent(this.config.baseURL + '/chat/completions');
          console.log('使用默认CORS代理:', ai_url);
        }
      } else {
        // 直接请求
        ai_url = this.config.baseURL + '/chat/completions';
        console.log('直接请求:', ai_url);
      }

      const response = await fetch(ai_url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(requestBody),
        // 添加mode来处理CORS
        mode: 'cors',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: OpenAIResponse = await response.json();
      return data;
    } catch (error) {
      console.error('OpenAI API request failed:', error);

      // 提供更详细的错误信息
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        if (error.message.includes('Content Security Policy')) {
          throw new Error('CSP错误：请检查Content Security Policy设置是否允许连接到API服务器');
        } else if (error.message.includes('CORS')) {
          throw new Error('CORS错误：API服务器不允许跨域请求，请在Electron环境中使用或配置代理服务器');
        } else {
          throw new Error('网络连接失败：请检查网络连接和API服务器地址是否正确');
        }
      }

      throw error;
    }
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const testMessages: OpenAIMessage[] = [
        { role: 'user', content: 'Hello, this is a test message.' }
      ];
      
      await this.sendMessage(testMessages, true);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 解析工具调用（简化版本）
  parseMCPToolCalls(content: string): Array<{ name: string; arguments: any }> {
    const toolCalls: Array<{ name: string; arguments: any }> = [];
    
    // 查找工具调用模式，例如：[TOOL:file_search]{"query": "example"}[/TOOL]
    const toolCallRegex = /\[TOOL:(\w+)\](.*?)\[\/TOOL\]/gs;
    let match;
    
    while ((match = toolCallRegex.exec(content)) !== null) {
      const toolName = match[1];
      const argsString = match[2].trim();
      
      try {
        const args = JSON.parse(argsString);
        toolCalls.push({
          name: toolName,
          arguments: args
        });
      } catch (error) {
        console.error(`Failed to parse tool arguments for ${toolName}:`, error);
      }
    }
    
    return toolCalls;
  }

  // 模拟文件搜索工具
  async executeFileSearch(query: string): Promise<any> {
    console.log(`[Mock] Executing file search for: ${query}`);
    
    // 模拟搜索结果
    return [
      {
        filePath: '/mock/path/example.txt',
        fileName: 'example.txt',
        fileSize: 1024,
        mimeType: 'text/plain',
        relevanceScore: 0.8,
        matchedLines: [
          { lineNumber: 1, content: `This file contains information about ${query}` }
        ]
      }
    ];
  }

  // 处理带工具调用的对话
  async sendMessageWithTools(messages: OpenAIMessage[], needsProxy: boolean): Promise<{ response: string; toolResults: any[] }> {
    try {
      const response = await this.sendMessage(messages, needsProxy);
      const assistantMessage = response.choices[0]?.message?.content || '';
      
      // 解析工具调用
      const toolCalls = this.parseMCPToolCalls(assistantMessage);
      const toolResults: any[] = [];
      
      if (toolCalls.length > 0) {
        // 执行工具调用
        for (const toolCall of toolCalls) {
          try {
            let result;
            switch (toolCall.name) {
              case 'file_search':
                result = await this.executeFileSearch(toolCall.arguments.query || '');
                break;
              default:
                result = { error: `Unknown tool: ${toolCall.name}` };
            }
            toolResults.push({ success: true, result });
          } catch (error) {
            toolResults.push({
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }
        
        // 如果有工具调用，需要再次发送消息包含工具结果
        if (toolResults.length > 0) {
          const toolResultsText = toolCalls.map((call, index) => 
            `[TOOL_RESULT:${call.name}]${JSON.stringify(toolResults[index].result)}[/TOOL_RESULT]`
          ).join('\n');
          
          const updatedMessages: OpenAIMessage[] = [
            ...messages,
            { role: 'assistant', content: assistantMessage },
            { role: 'user', content: `工具执行结果：\n${toolResultsText}\n\n请基于这些结果给出最终回答。` }
          ];
          
          const finalResponse = await this.sendMessage(updatedMessages, needsProxy);
          return { 
            response: finalResponse.choices[0]?.message?.content || '', 
            toolResults 
          };
        }
      }
      
      return { response: assistantMessage, toolResults };
    } catch (error) {
      throw error;
    }
  }
}

// 单例实例
export const browserOpenAIClient = new BrowserOpenAIClient();
