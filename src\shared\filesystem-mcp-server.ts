/**
 * 文件系统 MCP 服务器
 * 使用官方 @modelcontextprotocol/sdk 实现
 */

import * as fs from 'fs';
import * as path from 'path';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';

export interface FileSystemMCPConfig {
  allowedDirectories: string[];
  maxFileSize?: number;
  excludePatterns?: string[];
}

export class FileSystemMCPServer {
  private server: McpServer;
  private allowedDirectories: string[] = [];
  private maxFileSize: number;
  private excludePatterns: string[];
  
  constructor(config: FileSystemMCPConfig) {
    this.allowedDirectories = config.allowedDirectories;
    this.maxFileSize = config.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.excludePatterns = config.excludePatterns || [
      'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'target',
      '.DS_Store', 'Thumbs.db', '*.tmp', '*.temp', '*.log', '*.cache'
    ];
    
    this.server = new McpServer({
      name: 'filesystem-server',
      version: '1.0.0'
    });
    
    this.setupTools();
    this.setupResources();
  }

  private setupTools(): void {
    // 文件搜索工具
    this.server.registerTool(
      'search_files',
      {
        title: 'Search Files',
        description: 'Search for files by name or content',
        inputSchema: {
          query: z.string().describe('Search query'),
          path: z.string().optional().describe('Directory to search in'),
          fileTypes: z.array(z.string()).optional().describe('File extensions to include'),
          maxResults: z.number().optional().default(50).describe('Maximum number of results'),
          includeContent: z.boolean().optional().default(false).describe('Include file content in results')
        }
      },
      async ({ query, path: searchPath, fileTypes, maxResults = 50, includeContent = false }) => {
        try {
          const results = await this.searchFiles({
            query,
            searchPaths: searchPath ? [searchPath] : this.allowedDirectories,
            fileTypes,
            maxResults,
            includeContent
          });
          
          return {
            content: [{
              type: 'text',
              text: JSON.stringify(results, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error searching files: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    );

    // 读取文件工具
    this.server.registerTool(
      'read_file',
      {
        title: 'Read File',
        description: 'Read the contents of a file',
        inputSchema: {
          path: z.string().describe('Path to the file to read')
        }
      },
      async ({ path: filePath }) => {
        try {
          if (!this.isPathAllowed(filePath)) {
            throw new Error('Access to this path is not allowed');
          }
          
          const stats = await fs.promises.stat(filePath);
          if (stats.size > this.maxFileSize) {
            throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
          }
          
          const content = await fs.promises.readFile(filePath, 'utf-8');
          
          return {
            content: [{
              type: 'text',
              text: content
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error reading file: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    );

    // 列出目录工具
    this.server.registerTool(
      'list_directory',
      {
        title: 'List Directory',
        description: 'List files and directories in a path',
        inputSchema: {
          path: z.string().describe('Directory path to list')
        }
      },
      async ({ path: dirPath }) => {
        try {
          if (!this.isPathAllowed(dirPath)) {
            throw new Error('Access to this path is not allowed');
          }
          
          const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
          const items = entries.map(entry => ({
            name: entry.name,
            type: entry.isDirectory() ? 'directory' : 'file',
            path: path.join(dirPath, entry.name)
          }));
          
          return {
            content: [{
              type: 'text',
              text: JSON.stringify(items, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error listing directory: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    );

    // 写入文件工具
    this.server.registerTool(
      'write_file',
      {
        title: 'Write File',
        description: 'Write content to a file',
        inputSchema: {
          path: z.string().describe('Path to the file to write'),
          content: z.string().describe('Content to write to the file')
        }
      },
      async ({ path: filePath, content }) => {
        try {
          if (!this.isPathAllowed(filePath)) {
            throw new Error('Access to this path is not allowed');
          }
          
          // 确保目录存在
          const dir = path.dirname(filePath);
          await fs.promises.mkdir(dir, { recursive: true });
          
          await fs.promises.writeFile(filePath, content, 'utf-8');
          
          return {
            content: [{
              type: 'text',
              text: `Successfully wrote ${content.length} characters to ${filePath}`
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: 'text',
              text: `Error writing file: ${error instanceof Error ? error.message : 'Unknown error'}`
            }],
            isError: true
          };
        }
      }
    );
  }

  private setupResources(): void {
    // 文件资源
    this.server.registerResource(
      'file',
      'file://{path}',
      {
        title: 'File Content',
        description: 'Read file content as a resource'
      },
      async (uri, { path: filePath }) => {
        try {
          if (!this.isPathAllowed(filePath)) {
            throw new Error('Access to this path is not allowed');
          }
          
          const stats = await fs.promises.stat(filePath);
          if (stats.size > this.maxFileSize) {
            throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
          }
          
          const content = await fs.promises.readFile(filePath, 'utf-8');
          
          return {
            contents: [{
              uri: uri.href,
              text: content,
              mimeType: this.getMimeType(filePath)
            }]
          };
        } catch (error) {
          throw new Error(`Error reading file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    );
  }

  private async searchFiles(options: {
    query: string;
    searchPaths: string[];
    fileTypes?: string[];
    maxResults: number;
    includeContent: boolean;
  }): Promise<any[]> {
    const { query, searchPaths, fileTypes, maxResults, includeContent } = options;
    const results: any[] = [];
    
    for (const searchPath of searchPaths) {
      if (!this.isPathAllowed(searchPath)) {
        continue;
      }
      
      await this.searchInDirectory(searchPath, query, fileTypes, results, maxResults, includeContent);
      
      if (results.length >= maxResults) {
        break;
      }
    }
    
    return results.slice(0, maxResults);
  }

  private async searchInDirectory(
    dirPath: string,
    query: string,
    fileTypes: string[] | undefined,
    results: any[],
    maxResults: number,
    includeContent: boolean
  ): Promise<void> {
    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        if (results.length >= maxResults) {
          break;
        }
        
        const fullPath = path.join(dirPath, entry.name);
        
        // 跳过排除的模式
        if (this.shouldExclude(entry.name)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.searchInDirectory(fullPath, query, fileTypes, results, maxResults, includeContent);
        } else if (entry.isFile()) {
          // 检查文件类型
          if (fileTypes && fileTypes.length > 0) {
            const ext = path.extname(entry.name);
            if (!fileTypes.includes(ext)) {
              continue;
            }
          }
          
          // 检查文件名匹配
          if (entry.name.toLowerCase().includes(query.toLowerCase())) {
            const result: any = {
              path: fullPath,
              name: entry.name,
              type: 'file'
            };
            
            if (includeContent) {
              try {
                const stats = await fs.promises.stat(fullPath);
                if (stats.size <= this.maxFileSize) {
                  result.content = await fs.promises.readFile(fullPath, 'utf-8');
                }
              } catch (error) {
                // 忽略读取错误
              }
            }
            
            results.push(result);
          }
        }
      }
    } catch (error) {
      // 忽略目录访问错误
    }
  }

  private isPathAllowed(filePath: string): boolean {
    if (this.allowedDirectories.length === 0) {
      return true; // 如果没有限制，允许所有路径
    }
    
    const normalizedPath = path.resolve(filePath);
    return this.allowedDirectories.some(allowedDir => {
      const normalizedAllowedDir = path.resolve(allowedDir);
      return normalizedPath.startsWith(normalizedAllowedDir);
    });
  }

  private shouldExclude(fileName: string): boolean {
    return this.excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(fileName);
      }
      return fileName === pattern;
    });
  }

  private getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.js': 'text/javascript',
      '.ts': 'text/typescript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.xml': 'text/xml',
      '.yaml': 'text/yaml',
      '.yml': 'text/yaml'
    };
    
    return mimeTypes[ext] || 'text/plain';
  }

  async start(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('File system MCP server started');
  }

  getServer(): McpServer {
    return this.server;
  }
}

// 创建单例实例
export const fileSystemMCPServer = new FileSystemMCPServer({
  allowedDirectories: [process.cwd()], // 默认允许当前工作目录
  maxFileSize: 10 * 1024 * 1024, // 10MB
  excludePatterns: [
    'node_modules', '.git', '.svn', '.hg', 'dist', 'build', 'target',
    '.DS_Store', 'Thumbs.db', '*.tmp', '*.temp', '*.log', '*.cache'
  ]
});
