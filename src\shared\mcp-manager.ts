// MCP 管理器 - 整合本地和远程MCP服务器
import { MCPClient } from './mcp-client';
import { fileSearchMCP } from './file-search-mcp';
import { openaiClient, MCPToolCall, MCPToolResult } from './openai-client';
import { databaseManager } from './database';

export interface LocalMCPServer {
  id: string;
  name: string;
  tools: string[];
  handler: any;
}

export interface MCPServerInfo {
  id: string;
  name: string;
  type: 'local' | 'remote';
  status: 'connected' | 'disconnected' | 'error';
  tools: string[];
  url?: string;
}

export class MCPManager {
  private mcpClient: MCPClient;
  private localServers: Map<string, LocalMCPServer> = new Map();

  constructor() {
    this.mcpClient = new MCPClient();
    this.initializeLocalServers();
  }

  private initializeLocalServers(): void {
    // 注册本地文件搜索MCP服务器
    this.localServers.set('local_file_search', {
      id: 'local_file_search',
      name: '本地文件搜索',
      tools: ['file_search', 'index_files', 'get_file_content'],
      handler: fileSearchMCP
    });

    console.log('Local MCP servers initialized');
  }

  // 获取所有MCP服务器信息
  async getAllServers(): Promise<MCPServerInfo[]> {
    const servers: MCPServerInfo[] = [];

    // 添加本地服务器
    for (const [id, server] of this.localServers) {
      servers.push({
        id,
        name: server.name,
        type: 'local',
        status: 'connected', // 本地服务器总是连接状态
        tools: server.tools
      });
    }

    // 添加远程服务器
    const remoteConnections = this.mcpClient.getAllConnections();
    for (const connection of remoteConnections) {
      servers.push({
        id: connection.id,
        name: connection.name,
        type: 'remote',
        status: connection.status as 'connected' | 'disconnected' | 'error',
        tools: connection.tools?.map(tool => tool.name) || [],
        url: connection.url
      });
    }

    return servers;
  }

  // 执行工具调用
  async executeToolCall(toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      // 首先检查本地服务器
      const localServer = this.findLocalServerForTool(toolCall.name);
      if (localServer) {
        return await this.executeLocalToolCall(localServer, toolCall);
      }

      // 然后检查远程服务器
      const remoteServerId = await this.findRemoteServerForTool(toolCall.name);
      if (remoteServerId) {
        return await this.executeRemoteToolCall(remoteServerId, toolCall);
      }

      return {
        success: false,
        error: `No MCP server found for tool: ${toolCall.name}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private findLocalServerForTool(toolName: string): LocalMCPServer | null {
    for (const [id, server] of this.localServers) {
      if (server.tools.includes(toolName)) {
        return server;
      }
    }
    return null;
  }

  private async findRemoteServerForTool(toolName: string): Promise<string | null> {
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected' && 
          connection.tools?.some(tool => tool.name === toolName)) {
        return connection.id;
      }
    }
    return null;
  }

  private async executeLocalToolCall(server: LocalMCPServer, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      if (server.id === 'local_file_search') {
        const result = await fileSearchMCP.handleToolCall(toolCall.name, toolCall.arguments);
        return { success: true, result };
      }

      return {
        success: false,
        error: `Unknown local MCP server: ${server.id}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async executeRemoteToolCall(serverId: string, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {

      // 由于现有的MCPClient接口不同，我们需要适配
      const connection = this.mcpClient.getAllConnections().find(c => c.id === serverId);
      if (!connection) {
        return {
          success: false,
          error: `Remote MCP server not found: ${serverId}`
        };
      }

      // 调用远程工具（这里需要根据实际的MCP协议实现）
      const result = await this.callRemoteTool(serverId, toolCall.name, toolCall.arguments);
      return { success: true, result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async callRemoteTool(serverId: string, toolName: string, args: any): Promise<any> {
    // 这里需要实现远程工具调用的具体逻辑
    // 暂时返回一个占位符
    throw new Error('Remote tool call not implemented yet');
  }

  // 添加远程MCP服务器
  async addRemoteServer(config: {
    id: string;
    name: string;
    url: string;
    auth?: any;
  }): Promise<boolean> {
    try {
      await this.mcpClient.connect({
        id: config.id,
        name: config.name,
        url: config.url,
        enabled: true,
        autoConnect: true,
        auth: config.auth || { type: 'none' }
      });
      
      // 保存到数据库
      await databaseManager.setSetting(`mcp_server_${config.id}`, JSON.stringify(config), 'mcp');
      
      return true;
    } catch (error) {
      console.error('Failed to add remote MCP server:', error);
      return false;
    }
  }

  // 移除远程MCP服务器
  async removeRemoteServer(serverId: string): Promise<void> {
    this.mcpClient.disconnect(serverId);
    await databaseManager.setSetting(`mcp_server_${serverId}`, '', 'mcp');
  }

  // 获取可用工具列表
  async getAvailableTools(): Promise<Array<{ serverId: string; serverName: string; serverType: 'local' | 'remote'; tools: string[] }>> {
    const result: Array<{ serverId: string; serverName: string; serverType: 'local' | 'remote'; tools: string[] }> = [];
    
    // 本地服务器工具
    for (const [id, server] of this.localServers) {
      result.push({
        serverId: id,
        serverName: server.name,
        serverType: 'local',
        tools: server.tools
      });
    }
    
    // 远程服务器工具
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        result.push({
          serverId: connection.id,
          serverName: connection.name,
          serverType: 'remote',
          tools: connection.tools?.map(tool => tool.name) || []
        });
      }
    }
    
    return result;
  }

  // 处理带有工具调用的AI对话
  async handleAIConversationWithTools(
    messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
    sessionId?: string
  ): Promise<{ response: string; toolResults: MCPToolResult[] }> {
    try {
      // 发送消息到OpenAI并处理工具调用
      const { response, toolResults } = await openaiClient.sendMessageWithTools(
        messages,
        (toolCall) => this.executeToolCall(toolCall)
      );

      const assistantResponse = response.choices[0]?.message?.content || '';

      // 保存消息到数据库
      if (sessionId) {
        // 保存用户消息
        const userMessage = messages[messages.length - 1];
        if (userMessage && userMessage.role === 'user') {
          await databaseManager.addChatMessage({
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            role: 'user',
            content: userMessage.content,
            sessionId
          });
        }

        // 保存助手回复
        await databaseManager.addChatMessage({
          id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          role: 'assistant',
          content: assistantResponse,
          sessionId
        });
      }

      return {
        response: assistantResponse,
        toolResults
      };
    } catch (error) {
      console.error('AI conversation with tools failed:', error);
      return {
        response: '抱歉，处理您的请求时出现了错误。',
        toolResults: []
      };
    }
  }

  // 初始化文件搜索索引
  async initializeFileSearchIndex(paths: string[]): Promise<{ success: boolean; message: string }> {
    try {
      const result = await fileSearchMCP.indexFiles({
        paths,
        fileTypes: ['.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.css', '.html', '.json'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        excludePatterns: ['node_modules', '.git', 'dist', 'build']
      });

      return {
        success: true,
        message: `Successfully indexed ${result.indexed} directories. ${result.errors.length} errors occurred.`
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 关闭所有连接
  async shutdown(): Promise<void> {
    await this.mcpClient.disconnectAll();
    console.log('MCP Manager shutdown complete');
  }
}

// 单例实例
export const mcpManager = new MCPManager();
