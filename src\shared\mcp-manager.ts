// MCP 管理器 - 整合本地和远程MCP服务器
import { MCPClient } from './mcp-client';
import { fileSystemMCPServer } from './filesystem-mcp-server';
import { openaiClient, MCPToolCall, MCPToolResult } from './openai-client';
import { databaseManager } from './database';
import { MCPServerConfig } from './types';

export interface LocalMCPServer {
  id: string;
  name: string;
  tools: string[];
  handler: any;
}

export interface MCPServerInfo {
  id: string;
  name: string;
  type: 'local' | 'remote';
  status: 'connected' | 'disconnected' | 'error';
  tools: string[];
  url?: string;
}

export class MCPManager {
  private mcpClient: MCPClient;
  private localServers: Map<string, LocalMCPServer> = new Map();
  private remoteServers: Map<string, MCPServerConfig> = new Map();

  constructor() {
    this.mcpClient = new MCPClient();
    this.initializeLocalServers();
    this.loadRemoteServers();
  }

  private initializeLocalServers(): void {

    // 注册本地文件系统服务器
    this.localServers.set('filesystem', {
      id: 'filesystem',
      name: '文件系统',
      tools: ['search_files', 'read_file', 'list_directory', 'write_file'],
      handler: fileSystemMCPServer
    });

    console.log('Local MCP servers initialized');
  }

  private async loadRemoteServers(): Promise<void> {
    try {
      // 从数据库加载远程MCP服务器配置
      const settings = await databaseManager.getSettingsByCategory('mcp');
      for (const setting of settings) {
        if (setting.key.startsWith('mcp_server_')) {
          try {
            const config = JSON.parse(setting.value) as MCPServerConfig;
            this.remoteServers.set(config.id, config);

            // 如果配置为自动连接，则尝试连接
            if (config.autoConnect) {
              await this.connectToRemoteServer(config.id);
            }
          } catch (error) {
            console.error(`Failed to parse MCP server config ${setting.key}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load remote MCP servers:', error);
    }
  }

  // 获取所有MCP服务器信息
  async getAllServers(): Promise<MCPServerInfo[]> {
    const servers: MCPServerInfo[] = [];

    // 添加本地服务器
    for (const [id, server] of this.localServers) {
      servers.push({
        id,
        name: server.name,
        type: 'local',
        status: 'connected', // 本地服务器总是连接状态
        tools: server.tools
      });
    }

    // 添加远程服务器
    const remoteConnections = this.mcpClient.getAllConnections();
    for (const connection of remoteConnections) {
      servers.push({
        id: connection.id,
        name: connection.name,
        type: 'remote',
        status: connection.status as 'connected' | 'disconnected' | 'error',
        tools: connection.tools?.map(tool => tool.name) || [],
        url: connection.url
      });
    }

    return servers;
  }

  // 获取所有可用的工具
  async getAllTools(): Promise<Array<{ name: string; description: string; serverId: string; serverName: string }>> {
    const tools: Array<{ name: string; description: string; serverId: string; serverName: string }> = [];

    // 添加本地工具
    for (const server of this.localServers.values()) {
      for (const toolName of server.tools) {
        tools.push({
          name: toolName,
          description: `Local tool from ${server.name}`,
          serverId: server.id,
          serverName: server.name
        });
      }
    }

    // 添加远程工具
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        for (const tool of connection.tools) {
          tools.push({
            name: tool.name,
            description: tool.description,
            serverId: connection.id,
            serverName: connection.name
          });
        }
      }
    }

    return tools;
  }

  // 执行工具调用
  async executeToolCall(toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      // 首先检查本地服务器
      const localServer = this.findLocalServerForTool(toolCall.name);
      if (localServer) {
        return await this.executeLocalToolCall(localServer, toolCall);
      }

      // 然后检查远程服务器
      const remoteServerId = await this.findRemoteServerForTool(toolCall.name);
      if (remoteServerId) {
        return await this.executeRemoteToolCall(remoteServerId, toolCall);
      }

      return {
        success: false,
        error: `No MCP server found for tool: ${toolCall.name}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private findLocalServerForTool(toolName: string): LocalMCPServer | null {
    for (const [id, server] of this.localServers) {
      if (server.tools.includes(toolName)) {
        return server;
      }
    }
    return null;
  }

  private async findRemoteServerForTool(toolName: string): Promise<string | null> {
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected' && 
          connection.tools?.some(tool => tool.name === toolName)) {
        return connection.id;
      }
    }
    return null;
  }

  private async executeLocalToolCall(server: LocalMCPServer, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      if (server.id === 'local_file_search') {
        const result = await fileSearchMCP.handleToolCall(toolCall.name, toolCall.arguments);
        return { success: true, result };
      }

      return {
        success: false,
        error: `Unknown local MCP server: ${server.id}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async executeRemoteToolCall(serverId: string, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {

      // 由于现有的MCPClient接口不同，我们需要适配
      const connection = this.mcpClient.getAllConnections().find(c => c.id === serverId);
      if (!connection) {
        return {
          success: false,
          error: `Remote MCP server not found: ${serverId}`
        };
      }

      // 调用远程工具（这里需要根据实际的MCP协议实现）
      const result = await this.callRemoteTool(serverId, toolCall.name, toolCall.arguments);
      return { success: true, result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async executeRemoteToolCall(serverId: string, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      const result = await this.mcpClient.callTool(serverId, toolCall.name, toolCall.arguments);
      return {
        success: true,
        result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async findRemoteServerForTool(toolName: string): Promise<string | null> {
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        const tool = connection.tools.find(t => t.name === toolName);
        if (tool) {
          return connection.id;
        }
      }
    }
    return null;
  }

  // 连接到远程MCP服务器
  private async connectToRemoteServer(serverId: string): Promise<boolean> {
    const config = this.remoteServers.get(serverId);
    if (!config) {
      console.error(`Remote server config not found: ${serverId}`);
      return false;
    }

    try {
      await this.mcpClient.connect(config);
      console.log(`Connected to remote MCP server: ${config.name}`);
      return true;
    } catch (error) {
      console.error(`Failed to connect to remote MCP server ${config.name}:`, error);
      return false;
    }
  }

  // 添加远程MCP服务器
  async addRemoteServer(config: MCPServerConfig): Promise<boolean> {
    try {
      // 保存配置
      this.remoteServers.set(config.id, config);

      // 保存到数据库
      await databaseManager.setSetting(`mcp_server_${config.id}`, JSON.stringify(config), 'mcp');

      // 如果设置为自动连接，则立即连接
      if (config.autoConnect) {
        await this.connectToRemoteServer(config.id);
      }

      return true;
    } catch (error) {
      console.error('Failed to add remote MCP server:', error);
      return false;
    }
  }

  // 移除远程MCP服务器
  async removeRemoteServer(serverId: string): Promise<boolean> {
    try {
      // 断开连接
      this.mcpClient.disconnect(serverId);

      // 从内存中移除
      this.remoteServers.delete(serverId);

      // 从数据库中移除
      await databaseManager.setSetting(`mcp_server_${serverId}`, '', 'mcp');

      return true;
    } catch (error) {
      console.error('Failed to remove remote MCP server:', error);
      return false;
    }
  }

  // 测试MCP服务器连接
  async testServerConnection(serverId: string): Promise<{ success: boolean; error?: string; tools?: string[]; resources?: string[] }> {
    try {
      const config = this.remoteServers.get(serverId);
      if (!config) {
        return { success: false, error: 'Server configuration not found' };
      }

      // 尝试连接
      const connection = await this.mcpClient.connect(config);

      // 获取工具和资源列表
      const tools = connection.tools.map(tool => tool.name);
      const resources = connection.resources.map(resource => resource.uri);

      return {
        success: true,
        tools,
        resources
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 移除远程MCP服务器
  async removeRemoteServer(serverId: string): Promise<void> {
    this.mcpClient.disconnect(serverId);
    await databaseManager.setSetting(`mcp_server_${serverId}`, '', 'mcp');
  }

  // 获取可用工具列表
  async getAvailableTools(): Promise<Array<{ serverId: string; serverName: string; serverType: 'local' | 'remote'; tools: string[] }>> {
    const result: Array<{ serverId: string; serverName: string; serverType: 'local' | 'remote'; tools: string[] }> = [];
    
    // 本地服务器工具
    for (const [id, server] of this.localServers) {
      result.push({
        serverId: id,
        serverName: server.name,
        serverType: 'local',
        tools: server.tools
      });
    }
    
    // 远程服务器工具
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        result.push({
          serverId: connection.id,
          serverName: connection.name,
          serverType: 'remote',
          tools: connection.tools?.map(tool => tool.name) || []
        });
      }
    }
    
    return result;
  }

  // 处理带有工具调用的AI对话
  async handleAIConversationWithTools(
    messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
    sessionId?: string
  ): Promise<{ response: string; toolResults: MCPToolResult[] }> {
    try {
      // 发送消息到OpenAI并处理工具调用
      const { response, toolResults } = await openaiClient.sendMessageWithTools(
        messages,
        (toolCall) => this.executeToolCall(toolCall)
      );

      const assistantResponse = response.choices[0]?.message?.content || '';

      // 保存消息到数据库
      if (sessionId) {
        // 保存用户消息
        const userMessage = messages[messages.length - 1];
        if (userMessage && userMessage.role === 'user') {
          await databaseManager.addChatMessage({
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            role: 'user',
            content: userMessage.content,
            sessionId
          });
        }

        // 保存助手回复
        await databaseManager.addChatMessage({
          id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          role: 'assistant',
          content: assistantResponse,
          sessionId
        });
      }

      return {
        response: assistantResponse,
        toolResults
      };
    } catch (error) {
      console.error('AI conversation with tools failed:', error);
      return {
        response: '抱歉，处理您的请求时出现了错误。',
        toolResults: []
      };
    }
  }

  // 初始化文件搜索索引
  async initializeFileSearchIndex(paths: string[]): Promise<{ success: boolean; message: string }> {
    try {
      const result = await fileSearchMCP.indexFiles({
        paths,
        fileTypes: ['.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.css', '.html', '.json'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        excludePatterns: ['node_modules', '.git', 'dist', 'build']
      });

      return {
        success: true,
        message: `Successfully indexed ${result.indexed} directories. ${result.errors.length} errors occurred.`
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 关闭所有连接
  async shutdown(): Promise<void> {
    await this.mcpClient.disconnectAll();
    console.log('MCP Manager shutdown complete');
  }
}

// 单例实例
export const mcpManager = new MCPManager();
